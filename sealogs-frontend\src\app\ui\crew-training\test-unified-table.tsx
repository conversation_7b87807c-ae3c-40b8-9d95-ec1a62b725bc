'use client'

import { UnifiedTrainingTable } from './unified-training-table'
import { UnifiedTrainingData } from './utils/crew-training-utils'

// Test data to validate the unified table functionality
const testData: UnifiedTrainingData[] = [
    // Overdue training
    {
        id: 1,
        dueDate: '2024-01-01',
        vesselID: 1,
        vessel: { id: 1, title: 'Test Vessel 1' },
        trainingTypeID: 1,
        trainingType: { id: 1, title: 'Fire Safety' },
        members: [
            { id: 1, firstName: '<PERSON>', surname: '<PERSON><PERSON>' },
            { id: 2, firstName: '<PERSON>', surname: '<PERSON>' }
        ],
        status: {
            class: 'bg-destructive/10 text-destructive',
            label: 'Overdue',
            isOverdue: true,
            dueWithinSevenDays: false
        },
        category: 'overdue',
        originalData: null
    },
    // Upcoming training
    {
        id: 2,
        dueDate: '2024-12-31',
        vesselID: 2,
        vessel: { id: 2, title: 'Test Vessel 2' },
        trainingTypeID: 2,
        trainingType: { id: 2, title: 'First Aid' },
        members: [
            { id: 3, firstName: '<PERSON>', surname: '<PERSON>' }
        ],
        status: {
            class: 'bg-warning/10 text-warning',
            label: 'Due Soon',
            isOverdue: false,
            dueWithinSevenDays: true
        },
        category: 'upcoming',
        originalData: null
    },
    // Completed training
    {
        id: 3,
        dueDate: '2024-06-15',
        vesselID: 1,
        vessel: { id: 1, title: 'Test Vessel 1' },
        trainingTypeID: 1,
        trainingType: { id: 1, title: 'Fire Safety' },
        members: [
            { id: 1, firstName: 'John', surname: 'Doe' },
            { id: 2, firstName: 'Jane', surname: 'Smith' }
        ],
        status: {
            class: '',
            label: 'Completed',
            isOverdue: false,
            dueWithinSevenDays: false
        },
        category: 'completed',
        originalData: {
            id: 3,
            date: '2024-06-15',
            trainer: {
                id: 4,
                firstName: 'Alice',
                surname: 'Wilson'
            },
            trainingTypes: {
                nodes: [
                    { id: 1, title: 'Fire Safety' },
                    { id: 2, title: 'Emergency Response' }
                ]
            },
            members: {
                nodes: [
                    { id: 1, firstName: 'John', surname: 'Doe' },
                    { id: 2, firstName: 'Jane', surname: 'Smith' }
                ]
            }
        }
    }
]

export default function TestUnifiedTable() {
    return (
        <div className="p-6 space-y-6">
            <h1 className="text-2xl font-bold">Unified Training Table Test</h1>
            
            <div className="space-y-4">
                <h2 className="text-lg font-semibold">Mixed Data (All Categories)</h2>
                <UnifiedTrainingTable
                    unifiedData={testData}
                    showToolbar={true}
                    pageSize={10}
                />
            </div>

            <div className="space-y-4">
                <h2 className="text-lg font-semibold">Overdue Only</h2>
                <UnifiedTrainingTable
                    unifiedData={testData.filter(item => item.category === 'overdue')}
                    showToolbar={false}
                    pageSize={10}
                />
            </div>

            <div className="space-y-4">
                <h2 className="text-lg font-semibold">Completed Only</h2>
                <UnifiedTrainingTable
                    unifiedData={testData.filter(item => item.category === 'completed')}
                    showToolbar={false}
                    pageSize={10}
                />
            </div>
        </div>
    )
}
